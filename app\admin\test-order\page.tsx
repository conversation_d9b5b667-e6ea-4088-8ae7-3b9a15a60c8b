"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

export default function TestOrderPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const createTestOrder = async () => {
    setLoading(true);
    setResult(null);
    
    try {
      const response = await fetch('/api/admin/create-test-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ error: 'Failed to create test order' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6 p-6">
      <h1 className="text-2xl font-semibold">Create Test Order</h1>
      
      <Card className="p-6">
        <div className="space-y-4">
          <p className="text-muted-foreground">
            This will create a test order with variant information (Cropped Zip Up Hoodie - Rustic Black, Medium)
            to test the admin orders page display.
          </p>
          
          <Button 
            onClick={createTestOrder} 
            disabled={loading}
            className="w-full"
          >
            {loading ? 'Creating Test Order...' : 'Create Test Order'}
          </Button>
          
          {result && (
            <div className="mt-4 p-4 border rounded-md">
              <pre className="text-sm">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}
